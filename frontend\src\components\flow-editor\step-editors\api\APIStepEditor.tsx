import { useState } from 'react'
import { validateStep, RpaStep } from '@rpa-project/shared'
import type { FortnoxCreateVoucherStep, FortnoxAccountMapping } from '@rpa-project/shared/dist/esm/types/steps/api'
import { BaseStepEditorProps, StepEditorLayout, CommonStepFields } from '../base'
import { TextAreaField, VariableField, SelectField } from '../base/FieldComponents'

type APIStep = FortnoxCreateVoucherStep

interface APIStepEditorProps extends BaseStepEditorProps {
  step: RpaStep
}

export function APIStepEditor({
  step,
  onSave,
  onCancel,
  compact = false,
  steps = [],
  currentStepIndex = 0
}: APIStepEditorProps) {
  const [editedStep, setEditedStep] = useState<APIStep>({ ...step } as unknown as APIStep)
  const [errors, setErrors] = useState<string[]>([])

  const handleSave = () => {
    const validation = validateStep(editedStep as unknown as RpaStep)
    if (!validation.isValid) {
      setErrors(validation.errors.map(e => e.message))
      return
    }

    setErrors([])
    onSave(editedStep as unknown as RpaStep)
  }

  const updateStep = (updates: Partial<APIStep>) => {
    setEditedStep((prev: APIStep) => ({ ...prev, ...updates } as APIStep))
  }

  const addAccountMapping = () => {
    const fortnoxStep = editedStep as FortnoxCreateVoucherStep
    const newMapping: FortnoxAccountMapping = {
      accountNumber: '',
      variableName: '',
      debitCredit: 'debit'
    }
    updateStep({
      accountMappings: [...(fortnoxStep.accountMappings || []), newMapping]
    })
  }

  const updateAccountMapping = (index: number, updates: Partial<FortnoxAccountMapping>) => {
    const fortnoxStep = editedStep as FortnoxCreateVoucherStep
    const mappings = [...(fortnoxStep.accountMappings || [])]
    mappings[index] = { ...mappings[index], ...updates }
    updateStep({ accountMappings: mappings })
  }

  const removeAccountMapping = (index: number) => {
    const fortnoxStep = editedStep as FortnoxCreateVoucherStep
    const mappings = [...(fortnoxStep.accountMappings || [])]
    mappings.splice(index, 1)
    updateStep({ accountMappings: mappings })
  }

  const renderFortnoxCreateVoucherFields = () => {
    const fortnoxStep = editedStep as FortnoxCreateVoucherStep

    return (
      <>
        <TextAreaField
          label="Beskrivning"
          value={fortnoxStep.description || ''}
          onChange={(value) => updateStep({ description: value })}
          placeholder="Beskrivning av verifikationen"
          compact={compact}
        />

        <div style={{ display: 'grid', gridTemplateColumns: compact ? '1fr' : '1fr 1fr', gap: '1rem' }}>
          <SelectField
            label="Verifikationsserie"
            value={fortnoxStep.voucherSeries || 'A'}
            onChange={(value) => updateStep({ voucherSeries: value })}
            options={[
              { value: 'A', label: 'A - Allmän' },
              { value: 'B', label: 'B - Bank' },
              { value: 'C', label: 'C - Kassa' },
              { value: 'D', label: 'D - Diverse' }
            ]}
            compact={compact}
          />

          <VariableField
            label="Transaktionsdatum"
            value={fortnoxStep.transactionDate || ''}
            onChange={(value) => updateStep({ transactionDate: value })}
            placeholder="YYYY-MM-DD eller välj variabel"
            steps={steps}
            currentStepIndex={currentStepIndex}
            compact={compact}
          />
        </div>

        <div style={{ marginTop: '1.5rem' }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '1rem'
          }}>
            <h4 style={{ margin: 0, fontSize: compact ? '0.875rem' : '1rem', fontWeight: '600' }}>
              Kontomappningar
            </h4>
            <button
              type="button"
              onClick={addAccountMapping}
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: '#fd746c',
                color: 'white',
                border: 'none',
                borderRadius: '0.375rem',
                fontSize: compact ? '0.75rem' : '0.875rem',
                cursor: 'pointer'
              }}
            >
              + Lägg till konto
            </button>
          </div>

          {(fortnoxStep.accountMappings || []).map((mapping: FortnoxAccountMapping, index: number) => (
            <div key={index} style={{
              border: '1px solid #e5e7eb',
              borderRadius: '0.5rem',
              padding: '1rem',
              marginBottom: '1rem',
              backgroundColor: '#f9fafb'
            }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '1rem'
              }}>
                <span style={{ fontWeight: '500', fontSize: compact ? '0.875rem' : '1rem' }}>
                  Konto {index + 1}
                </span>
                <button
                  type="button"
                  onClick={() => removeAccountMapping(index)}
                  style={{
                    padding: '0.25rem 0.5rem',
                    backgroundColor: '#ef4444',
                    color: 'white',
                    border: 'none',
                    borderRadius: '0.25rem',
                    fontSize: '0.75rem',
                    cursor: 'pointer'
                  }}
                >
                  Ta bort
                </button>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: compact ? '1fr' : '1fr 1fr', gap: '1rem' }}>
                <VariableField
                  label="Kontonummer"
                  value={mapping.accountNumber}
                  onChange={(value) => updateAccountMapping(index, { accountNumber: value })}
                  placeholder="Ange kontonummer"
                  steps={steps}
                  currentStepIndex={currentStepIndex}
                  compact={compact}
                />

                <VariableField
                  label="Variabel med belopp"
                  value={mapping.variableName}
                  onChange={(value) => updateAccountMapping(index, { variableName: value })}
                  placeholder="Välj variabel med belopp"
                  steps={steps}
                  currentStepIndex={currentStepIndex}
                  compact={compact}
                />
              </div>

              <div style={{ marginTop: '1rem' }}>
                <SelectField
                  label="Debet/Kredit"
                  value={mapping.debitCredit}
                  onChange={(value) => updateAccountMapping(index, { debitCredit: value as 'debit' | 'credit' })}
                  options={[
                    { value: 'debit', label: 'Debet' },
                    { value: 'credit', label: 'Kredit' }
                  ]}
                  compact={compact}
                />
              </div>
            </div>
          ))}

          {(!fortnoxStep.accountMappings || fortnoxStep.accountMappings.length === 0) && (
            <div style={{
              padding: '2rem',
              textAlign: 'center',
              backgroundColor: '#f9fafb',
              borderRadius: '0.5rem',
              color: '#6b7280',
              fontSize: compact ? '0.875rem' : '1rem'
            }}>
              <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>📊</div>
              <div>Inga kontomappningar ännu</div>
              <div style={{ fontSize: '0.875rem', marginTop: '0.5rem' }}>
                Klicka på "Lägg till konto" för att börja
              </div>
            </div>
          )}
        </div>
      </>
    )
  }

  const renderAPIFields = () => {
    switch (editedStep.type) {
      case 'fortnoxCreateVoucher':
        return renderFortnoxCreateVoucherFields()
      default:
        return (
          <div style={{
            padding: '2rem',
            textAlign: 'center',
            backgroundColor: '#f9fafb',
            borderRadius: '0.5rem',
            color: '#6b7280',
            fontSize: compact ? '0.875rem' : '1rem'
          }}>
            <div style={{ marginBottom: '0.5rem', fontSize: '2rem' }}>🚧</div>
            <div style={{ fontWeight: '500', marginBottom: '0.5rem' }}>API Steps Coming Soon</div>
            <div style={{ fontSize: '0.875rem' }}>
              API step editors will be implemented in future versions.
            </div>
          </div>
        )
    }
  }

  const getTitle = () => {
    switch (editedStep.type) {
      case 'fortnoxCreateVoucher':
        return compact ? 'Skapa Fortnox Verifikation' : 'Konfigurera Fortnox Verifikation'
      default:
        return compact ? 'Konfigurera API-steg' : 'Edit API Step'
    }
  }

  return (
    <StepEditorLayout
      title={getTitle()}
      errors={errors}
      onSave={handleSave}
      onCancel={onCancel}
      compact={compact}
    >
      {renderAPIFields()}
      <CommonStepFields 
        step={editedStep} 
        updateStep={updateStep} 
        compact={compact} 
      />
    </StepEditorLayout>
  )
}
