import { useState } from 'react'
import { validateStep, ExtractTextStep, TakeScreenshotStep, DownloadFileStep } from '@rpa-project/shared'
import { BaseStepEditorProps, StepEditorLayout, CommonStepFields } from '../base'
import { SelectorField, VariableField, TextField, CheckboxField } from '../base/FieldComponents'

type ExtractionStep = ExtractTextStep | TakeScreenshotStep | DownloadFileStep

interface ExtractionStepEditorProps extends BaseStepEditorProps {
  step: ExtractionStep
}

export function ExtractionStepEditor({ 
  step, 
  onSave, 
  onCancel, 
  compact = false, 
  steps = [], 
  currentStepIndex = 0 
}: ExtractionStepEditorProps) {
  const [editedStep, setEditedStep] = useState<ExtractionStep>({ ...step })
  const [errors, setErrors] = useState<string[]>([])

  const handleSave = () => {
    const validation = validateStep(editedStep)
    if (!validation.isValid) {
      setErrors(validation.errors.map(e => e.message))
      return
    }

    setErrors([])
    onSave(editedStep)
  }

  const updateStep = (updates: Partial<ExtractionStep>) => {
    setEditedStep(prev => ({ ...prev, ...updates } as ExtractionStep))
  }

  const renderExtractionFields = () => {
    switch (editedStep.type) {
      case 'extractText':
        const extractStep = editedStep as ExtractTextStep
        return (
          <>
            <SelectorField
              label="Element Selector"
              value={extractStep.selector || ''}
              onChange={(selector) => updateStep({ selector })}
              placeholder="Element to extract text from"
              compact={compact}
            />
            <TextField
              label="Variable Name"
              value={extractStep.variableName || ''}
              onChange={(value) => updateStep({ variableName: value })}
              placeholder="Variable to store the text"
              compact={compact}
            />
          </>
        )

      case 'takeScreenshot':
        const screenshotStep = editedStep as TakeScreenshotStep
        return (
          <>
            <TextField
              label="File Path (optional)"
              value={screenshotStep.path || ''}
              onChange={(value) => updateStep({ path: value })}
              placeholder="screenshots/my-screenshot.png"
              compact={compact}
            />
            <TextField
              label="Variable Name (for base64)"
              value={screenshotStep.variableName || ''}
              onChange={(value) => updateStep({ variableName: value })}
              placeholder="screenshotBase64"
              compact={compact}
            />
            <CheckboxField
              label="Full Page Screenshot"
              checked={screenshotStep.fullPage || false}
              onChange={(checked) => updateStep({ fullPage: checked })}
              compact={compact}
            />
          </>
        )

      case 'downloadFile':
        const downloadStep = editedStep as DownloadFileStep
        return (
          <>
            <SelectorField
              label="Trigger Selector (valfritt)"
              value={downloadStep.triggerSelector || ''}
              onChange={(selector) => updateStep({ triggerSelector: selector })}
              placeholder="a[download], button"
              compact={compact}
            />
            <VariableField
              label="Filnamn (valfritt)"
              value={downloadStep.filename || ''}
              onChange={(value) => updateStep({ filename: value })}
              placeholder="document.pdf"
              steps={steps}
              currentStepIndex={currentStepIndex}
              compact={compact}
            />
            <TextField
              label="Variabelnamn för base64 (valfritt)"
              value={downloadStep.variableName || ''}
              onChange={(value) => updateStep({ variableName: value })}
              placeholder="fileContent"
              compact={compact}
            />
            <CheckboxField
              label="Forcera nedladdning"
              description="lägg till download-attribut"
              checked={downloadStep.forceDownload === true}
              onChange={(checked) => updateStep({ forceDownload: checked })}
              compact={compact}
            />
            <CheckboxField
              label="Spara fil till disk"
              checked={downloadStep.saveToFile === true}
              onChange={(checked) => updateStep({ saveToFile: checked })}
              compact={compact}
            />
          </>
        )

      default:
        return (
          <div className="text-gray-500">
            No specific configuration available for this step type.
          </div>
        )
    }
  }

  const getTitle = () => {
    const stepNames = {
      extractText: 'Extract Text',
      takeScreenshot: 'Take Screenshot',
      downloadFile: 'Download File'
    }
    
    const stepName = stepNames[editedStep.type as keyof typeof stepNames] || editedStep.type
    return compact ? `Konfigurera ${stepName}-steg` : `Edit ${stepName} Step`
  }

  return (
    <StepEditorLayout
      title={getTitle()}
      errors={errors}
      onSave={handleSave}
      onCancel={onCancel}
      compact={compact}
    >
      {renderExtractionFields()}
      <CommonStepFields 
        step={editedStep} 
        updateStep={updateStep} 
        compact={compact} 
      />
    </StepEditorLayout>
  )
}
