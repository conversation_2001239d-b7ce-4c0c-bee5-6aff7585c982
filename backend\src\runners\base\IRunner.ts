import { RpaFlow, RpaStep, FlowSettings, ExecutionLog } from '@rpa-project/shared';

/**
 * Context passed to step execution
 */
export interface RunnerContext {
  variables: Record<string, any>;
  onLog: (log: Omit<ExecutionLog, 'timestamp'>) => void;
  cancellationChecker?: () => Promise<boolean>;
  flowId?: string;
  customerId?: string;
}

/**
 * Result of step execution
 */
export interface StepExecutionResult {
  success: boolean;
  error?: string;
  variables?: Record<string, any>;
}

/**
 * Abstract interface for all RPA execution engines
 */
export interface IRunner {
  /**
   * Initialize the runner with flow settings and variables
   */
  initialize(settings: FlowSettings, variables: Record<string, any>): Promise<void>;

  /**
   * Execute a single RPA step
   */
  executeStep(step: RpaStep, context: RunnerContext): Promise<StepExecutionResult>;

  /**
   * Execute an entire flow (optional - can use default implementation)
   */
  executeFlow?(flow: RpaFlow, variables: Record<string, any>): Promise<Record<string, any>>;

  /**
   * Set log handler for the runner
   */
  setLogHandler(handler: (log: Omit<ExecutionLog, 'timestamp'>) => void): void;

  /**
   * Set cancellation checker for the runner
   */
  setCancellationChecker(checker: () => Promise<boolean>): void;

  /**
   * Check if the runner can handle a specific step type
   */
  canHandleStep(stepType: string): boolean;

  /**
   * Get list of step types this runner supports
   */
  getSupportedStepTypes(): string[];

  /**
   * Clean up resources
   */
  cleanup(): Promise<void>;
}
