import { RpaStepBase } from './base';

// API steps (future implementation)
// These are placeholder types for future API integration functionality

export interface ApiCallStep extends RpaStepBase {
  type: 'apiCall';
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  variableName?: string; // Variable name to store response
}

export interface ApiAuthStep extends RpaStepBase {
  type: 'apiAuth';
  authType: 'bearer' | 'basic' | 'apikey';
  credentialId: string;
  variableName?: string; // Variable name to store auth token
}

// Fortnox API steps

/**
 * Account mapping for Fortnox voucher entries
 */
export interface FortnoxAccountMapping {
  accountNumber: string;
  accountName?: string;
  variableName: string;
  debitCredit: 'debit' | 'credit';
}

/**
 * Fortnox Create Voucher step
 * Fetches chart of accounts and creates a voucher with mapped variables
 */
export interface FortnoxCreateVoucherStep extends RpaStepBase {
  type: 'fortnoxCreateVoucher';
  description?: string; // Voucher description
  accountMappings: FortnoxAccountMapping[]; // Account mappings with variables
  voucherSeries?: string; // Optional voucher series (defaults to 'A')
  transactionDate?: string; // Optional transaction date (defaults to today)
}
