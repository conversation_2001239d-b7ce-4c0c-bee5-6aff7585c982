import { RpaFlow, FlowExecution } from '@rpa-project/shared'
import { Modal } from '../ui/Modal'
import { useState } from 'react'

interface VariablesModalProps {
  isOpen: boolean
  onClose: () => void
  flow: RpaFlow
  currentExecution: FlowExecution | null
}

// Helper function to detect file type from base64 data
function detectFileType(base64Data: string): { type: 'image' | 'pdf' | 'text' | 'unknown', mimeType: string, icon: string } {
  try {
    // Clean the base64 data
    const cleanData = base64Data.replace(/\s/g, '');

    // Check for common file signatures in base64
    const header = cleanData.substring(0, 50);

    // Image formats - more comprehensive detection
    if (header.startsWith('iVBORw0KGgo')) {
      return { type: 'image', mimeType: 'image/png', icon: '🖼️' };
    }
    if (header.startsWith('/9j/') || header.startsWith('FFD8')) {
      return { type: 'image', mimeType: 'image/jpeg', icon: '🖼️' };
    }
    if (header.startsWith('R0lGOD')) {
      return { type: 'image', mimeType: 'image/gif', icon: '🖼️' };
    }
    if (header.startsWith('UklGR')) {
      return { type: 'image', mimeType: 'image/webp', icon: '🖼️' };
    }

    // PDF
    if (header.startsWith('JVBERi0')) {
      return { type: 'pdf', mimeType: 'application/pdf', icon: '📄' };
    }

    // Try to decode and check if it's readable text
    try {
      const decoded = atob(cleanData.substring(0, 200));
      // Check if it's mostly printable ASCII
      const printableRatio = (decoded.match(/[\x20-\x7E\s]/g) || []).length / decoded.length;
      if (printableRatio > 0.8) {
        return { type: 'text', mimeType: 'text/plain', icon: '📝' };
      }
    } catch (e) {
      // Not valid base64 or not text
    }

    // Default to image if it looks like base64 but we can't identify it
    // (many screenshots might not have perfect headers)
    return { type: 'image', mimeType: 'image/png', icon: '🖼️' };
  } catch (e) {
    return { type: 'unknown', mimeType: 'application/octet-stream', icon: '📁' };
  }
}

// Component for rendering base64 file preview
function Base64Preview({ value, variableName }: { value: string, variableName: string }) {
  const [showFullPreview, setShowFullPreview] = useState(false);
  const fileInfo = detectFileType(value);

  const downloadFile = () => {
    try {
      const blob = new Blob([Uint8Array.from(atob(value), c => c.charCodeAt(0))], { type: fileInfo.mimeType });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${variableName}.${fileInfo.type === 'image' ? 'png' : fileInfo.type === 'pdf' ? 'pdf' : 'bin'}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to download file:', error);
    }
  };

  return (
    <details style={{ cursor: 'pointer' }}>
      <summary style={{
        color: '#fd746c',
        fontWeight: '500',
        userSelect: 'none',
        display: 'flex',
        alignItems: 'center',
        gap: '0.5rem'
      }}>
        {fileInfo.icon} {fileInfo.type.toUpperCase()} fil ({Math.round(value.length * 0.75 / 1024)} KB) - klicka för förhandsgranskning
      </summary>
      <div style={{
        marginTop: '0.75rem',
        padding: '1rem',
        backgroundColor: '#f9fafb',
        borderRadius: '0.5rem',
        border: '1px solid #e5e7eb'
      }}>
        {/* Preview based on file type */}
        {fileInfo.type === 'image' && (
          <div style={{ marginBottom: '1rem' }}>
            <img
              src={`data:${fileInfo.mimeType};base64,${value}`}
              alt={`Preview of ${variableName}`}
              style={{
                maxWidth: '100%',
                maxHeight: '300px',
                border: '1px solid #d1d5db',
                borderRadius: '0.375rem',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
              }}
              onError={(e) => {
                (e.target as HTMLImageElement).style.display = 'none';
              }}
            />
          </div>
        )}

        {fileInfo.type === 'pdf' && (
          <div style={{
            padding: '2rem',
            textAlign: 'center',
            backgroundColor: '#fef2f2',
            borderRadius: '0.375rem',
            marginBottom: '1rem'
          }}>
            <div style={{ fontSize: '3rem', marginBottom: '0.5rem' }}>📄</div>
            <div style={{ color: '#7f1d1d', fontWeight: '500' }}>PDF-fil</div>
            <div style={{ color: '#991b1b', fontSize: '0.875rem' }}>Klicka på "Ladda ner" för att öppna</div>
          </div>
        )}

        {fileInfo.type === 'text' && (
          <div style={{
            marginBottom: '1rem',
            padding: '0.75rem',
            backgroundColor: '#ffffff',
            border: '1px solid #d1d5db',
            borderRadius: '0.375rem',
            fontFamily: 'Monaco, Consolas, "Courier New", monospace',
            fontSize: '0.875rem',
            maxHeight: '200px',
            overflow: 'auto'
          }}>
            {(() => {
              try {
                return atob(value);
              } catch (e) {
                return 'Kunde inte avkoda textinnehåll';
              }
            })()}
          </div>
        )}

        {fileInfo.type === 'unknown' && (
          <div style={{
            padding: '2rem',
            textAlign: 'center',
            backgroundColor: '#f3f4f6',
            borderRadius: '0.375rem',
            marginBottom: '1rem'
          }}>
            <div style={{ fontSize: '3rem', marginBottom: '0.5rem' }}>📁</div>
            <div style={{ color: '#374151', fontWeight: '500' }}>Okänd filtyp</div>
            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>Ladda ner för att öppna</div>
          </div>
        )}

        {/* Action buttons */}
        <div style={{
          display: 'flex',
          gap: '0.75rem',
          alignItems: 'center',
          flexWrap: 'wrap'
        }}>
          <button
            onClick={downloadFile}
            style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#fd746c',
              color: 'white',
              border: 'none',
              borderRadius: '0.375rem',
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.375rem'
            }}
          >
            💾 Ladda ner
          </button>

          <button
            onClick={() => setShowFullPreview(!showFullPreview)}
            style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#f3f4f6',
              color: '#374151',
              border: '1px solid #d1d5db',
              borderRadius: '0.375rem',
              fontSize: '0.875rem',
              cursor: 'pointer'
            }}
          >
            {showFullPreview ? '🙈 Dölj' : '👁️ Visa'} base64
          </button>

          <div style={{
            fontSize: '0.75rem',
            color: '#6b7280',
            marginLeft: 'auto'
          }}>
            {value.length.toLocaleString()} tecken
          </div>
        </div>

        {/* Full base64 preview */}
        {showFullPreview && (
          <div style={{
            marginTop: '1rem',
            padding: '0.75rem',
            backgroundColor: '#ffffff',
            border: '1px solid #d1d5db',
            borderRadius: '0.375rem',
            fontFamily: 'Monaco, Consolas, "Courier New", monospace',
            fontSize: '0.75rem',
            color: '#6b7280',
            wordBreak: 'break-all',
            maxHeight: '150px',
            overflow: 'auto'
          }}>
            {value.substring(0, 1000)}{value.length > 1000 ? '...' : ''}
          </div>
        )}
      </div>
    </details>
  );
}

export function VariablesModal({ isOpen, onClose, flow, currentExecution }: VariablesModalProps) {
  const getVariablesFromLogs = (execution: FlowExecution | null): Record<string, any> => {
    if (!execution || !execution.logs) return {}
    
    const variables: Record<string, any> = {}
    
    // Extract variables from log data
    execution.logs.forEach(log => {
      if (log.data && typeof log.data === 'object') {
        Object.assign(variables, log.data)
      }
    })
    
    return variables
  }

  const getExpectedVariables = (): Record<string, string> => {
    if (!flow) return {}
    
    const expectedVars: Record<string, string> = {}
    
    // Find steps that create variables
    flow.steps.forEach(step => {
      if (step.type === 'extractText' && (step as any).variableName) {
        expectedVars[(step as any).variableName] = `Text från: ${(step as any).selector}`
      }
      if (step.type === 'extractAttribute' && (step as any).variableName) {
        expectedVars[(step as any).variableName] = `Attribut ${(step as any).attribute} från: ${(step as any).selector}`
      }
      if (step.type === 'takeScreenshot' && (step as any).variableName) {
        expectedVars[(step as any).variableName] = `Base64 skärmdump${(step as any).path ? ` från: ${(step as any).path}` : ''}`
      }
      if (step.type === 'downloadFile' && (step as any).variableName) {
        expectedVars[(step as any).variableName] = `Base64 filinnehåll${(step as any).filename ? ` från: ${(step as any).filename}` : ''}`
      }

    })
    
    return expectedVars
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="🔧 Flödesvariabler"
      size="xl"
    >
      <div style={{ padding: '1.5rem' }}>
            {/* Expected Variables Section */}
            <div style={{ marginBottom: '2rem' }}>
              <h2 className="section-title" style={{ padding: 0, marginBottom: '0.75rem' }}>
                Förväntade variabler (från flödessteg)
              </h2>
              <div className="info-box">
                {(() => {
                  const expectedVars = getExpectedVariables()
                  return Object.keys(expectedVars).length > 0 ? (
                    <div style={{ display: 'grid', gap: '0.75rem' }}>
                      {Object.entries(expectedVars).map(([key, description]) => (
                        <div key={key} className="table-container" style={{
                          padding: 0,
                          marginBottom: 0
                        }}>
                          <div className="activity-table">
                            <table className="table">
                              <tbody>
                                <tr>
                                  <td style={{
                                    fontWeight: '500',
                                    color: '#1a0f0f',
                                    fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                                    width: '30%'
                                  }}>
                                    ${key}
                                  </td>
                                  <td className="secondary-text">
                                    {description}
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="empty-state">
                      <div className="empty-state-icon">📝</div>
                      <p className="empty-state-title">Inga variabler definierade</p>
                      <p className="empty-state-subtitle">Inga steg som skapar variabler hittades i detta flöde</p>
                    </div>
                  )
                })()}
              </div>
            </div>

            {/* Runtime Variables Section */}
            <div>
              <h2 className="section-title" style={{ padding: 0, marginBottom: '0.75rem' }}>
                Runtime-variabler (från senaste körning)
              </h2>
              <div className="table-container" style={{ padding: 0 }}>
                <div className="activity-table">
                  {(() => {
                    // Prioritize execution.results over log data for actual variable values
                    const logVariables = getVariablesFromLogs(currentExecution);
                    const runtimeVariables = {
                      ...logVariables,
                      ...(currentExecution?.results || {}) // Results override log data
                    }
                    return Object.keys(runtimeVariables).length > 0 ? (
                      <table className="table">
                        <thead>
                          <tr>
                            <th style={{ width: '30%' }}>Variabelnamn</th>
                            <th>Värde</th>
                          </tr>
                        </thead>
                        <tbody>
                          {Object.entries(runtimeVariables).map(([key, value]) => (
                            <tr key={key}>
                              <td style={{
                                fontWeight: '500',
                                color: '#1a0f0f',
                                fontFamily: 'Monaco, Consolas, "Courier New", monospace'
                              }}>
                                ${key}
                              </td>
                              <td>
                                <div style={{
                                  color: '#6b7280',
                                  fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                                  fontSize: '0.875rem',
                                  wordBreak: 'break-all',
                                  maxWidth: '400px',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis'
                                }}>
                                  {(() => {
                                    const stringValue = typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value);

                                    // Skip log-formatted base64 descriptions
                                    if (typeof value === 'string' && value.includes('base64 data (') && value.includes(' chars)')) {
                                      return value; // Show the description as-is for log entries
                                    }

                                    // Check if this looks like base64 data (long string with mostly base64 characters)
                                    if (typeof value === 'string' && value.length > 100) {
                                      // More flexible base64 detection - allow some whitespace and check if mostly base64 chars
                                      const cleanValue = value.replace(/\s/g, '');
                                      const base64Ratio = (cleanValue.match(/[A-Za-z0-9+/=]/g) || []).length / cleanValue.length;

                                      if (base64Ratio > 0.8 && cleanValue.length > 100) {
                                        return <Base64Preview value={cleanValue} variableName={key} />;
                                      }
                                    }

                                    return stringValue;
                                  })()}
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    ) : (
                      <div className="empty-state">
                        <div className="empty-state-icon">🔍</div>
                        <p className="empty-state-title">Inga runtime-variabler</p>
                        <p className="empty-state-subtitle">
                          {currentExecution ? 'Inga runtime-variabler från senaste körning' : 'Ingen körning tillgänglig - kör flödet för att se runtime-variabler'}
                        </p>
                      </div>
                    )
                  })()}
                </div>
              </div>
            </div>
      </div>
    </Modal>
  )
}
