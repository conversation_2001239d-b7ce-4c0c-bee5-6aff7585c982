import { RpaFlow, RpaStep, ExecutionLog } from '@rpa-project/shared';
import { getRunnerFactory, RunnerFactoryConfig } from './factory';
import { IRunner, RunnerContext } from './base';
import { getRunnerForStep, isRunnerImplemented, RunnerType } from './registry';

/**
 * Orchestrates the execution of RPA flows using multiple runners
 */
export class FlowExecutor {
  private runnerFactory = getRunnerFactory();
  private executionId: string;
  private logHandler: (log: Omit<ExecutionLog, 'timestamp'>) => void;
  private cancellationChecker?: () => Promise<boolean>;
  private initializedRunners = new Map<string, IRunner>();

  constructor(
    executionId: string,
    logHandler: (log: Omit<ExecutionLog, 'timestamp'>) => void,
    cancellationChecker?: () => Promise<boolean>
  ) {
    this.executionId = executionId;
    this.logHandler = logHandler;
    this.cancellationChecker = cancellationChecker;
  }

  /**
   * Execute an entire RPA flow using appropriate runners for each step
   */
  async executeFlow(flow: RpaFlow, variables: Record<string, any> = {}): Promise<Record<string, any>> {
    this.logHandler({
      level: 'info',
      message: `Starting execution of flow: ${flow.name} (${flow.steps.length} steps)`
    });

    // Analyze the flow to understand what runners are needed
    const analysis = this.runnerFactory.analyzeFlow(flow);
    
    if (analysis.unsupportedSteps.length > 0) {
      const errorMessage = `Flow contains unsupported step types: ${analysis.unsupportedSteps.join(', ')}`;
      this.logHandler({
        level: 'error',
        message: errorMessage
      });
      throw new Error(errorMessage);
    }

    this.logHandler({
      level: 'info',
      message: `Flow requires runners: ${analysis.requiredRunners.join(', ')}`
    });

    const context: RunnerContext = {
      variables: { ...variables },
      onLog: this.logHandler,
      cancellationChecker: this.cancellationChecker,
      flowId: flow.id,
      customerId: flow.customerId
    };

    try {
      // Check if this is a single-runner flow (e.g., all web automation steps)
      if (analysis.requiredRunners.length === 1 && analysis.requiredRunners[0] === RunnerType.PLAYWRIGHT) {
        // Use PlaywrightRunner's own executeFlow method which includes delays
        const runner = this.runnerFactory.getOrCreateRunner(this.executionId, RunnerType.PLAYWRIGHT, {
          logHandler: this.logHandler,
          cancellationChecker: this.cancellationChecker
        });

        this.logHandler({
          level: 'info',
          message: 'Using PlaywrightRunner executeFlow for web automation flow'
        });

        return await runner.executeFlow!(flow, variables);
      }

      // Multi-runner flow - execute step by step with appropriate runners
      this.logHandler({
        level: 'info',
        message: 'Executing multi-runner flow step by step'
      });

      for (const step of flow.steps) {
        // Check for cancellation
        if (this.cancellationChecker && await this.cancellationChecker()) {
          throw new Error('Flow execution was cancelled');
        }

        // Get the appropriate runner for this step
        const runnerType = getRunnerForStep(step.type);
        
        if (!isRunnerImplemented(runnerType)) {
          throw new Error(`Runner type '${runnerType}' is not implemented for step '${step.type}'`);
        }

        const runner = this.runnerFactory.getOrCreateRunner(this.executionId, runnerType, {
          logHandler: this.logHandler,
          cancellationChecker: this.cancellationChecker
        });

        // Initialize runner if not already done
        if (!this.initializedRunners.has(runnerType)) {
          await runner.initialize(flow.settings || {}, context.variables);
          this.initializedRunners.set(runnerType, runner);
        }

        // Execute the step
        const result = await runner.executeStep(step, context);
        
        if (!result.success) {
          throw new Error(result.error || `Step ${step.type} failed`);
        }

        // Update variables with any new values from the step
        if (result.variables) {
          Object.assign(context.variables, result.variables);
        }
      }

      return context.variables;
    } finally {
      // Cleanup will be handled by the factory
      await this.runnerFactory.cleanupExecution(this.executionId);
    }
  }

  /**
   * Execute a single step using the appropriate runner
   */
  async executeStep(step: RpaStep, variables: Record<string, any> = {}): Promise<{
    success: boolean;
    error?: string;
    variables?: Record<string, any>;
  }> {
    const runnerType = getRunnerForStep(step.type);
    
    if (!isRunnerImplemented(runnerType)) {
      return {
        success: false,
        error: `Runner type '${runnerType}' is not implemented for step '${step.type}'`
      };
    }

    const runner = this.runnerFactory.getOrCreateRunner(this.executionId, runnerType, {
      logHandler: this.logHandler,
      cancellationChecker: this.cancellationChecker
    });

    // Initialize runner if not already done
    if (!this.initializedRunners.has(runnerType)) {
      await runner.initialize({}, variables);
      this.initializedRunners.set(runnerType, runner);
    }

    const context: RunnerContext = {
      variables,
      onLog: this.logHandler,
      cancellationChecker: this.cancellationChecker
    };

    return await runner.executeStep(step, context);
  }

  /**
   * Get analysis of what runners a flow requires
   */
  analyzeFlow(flow: RpaFlow) {
    return this.runnerFactory.analyzeFlow(flow);
  }

  /**
   * Check if a step type is supported
   */
  isStepTypeSupported(stepType: string): boolean {
    return this.runnerFactory.isStepTypeSupported(stepType);
  }

  /**
   * Get all supported step types
   */
  getSupportedStepTypes(): string[] {
    return this.runnerFactory.getSupportedStepTypes();
  }

  /**
   * Clean up all runners for this execution
   */
  async cleanup(): Promise<void> {
    await this.runnerFactory.cleanupExecution(this.executionId);
    this.initializedRunners.clear();
  }
}
